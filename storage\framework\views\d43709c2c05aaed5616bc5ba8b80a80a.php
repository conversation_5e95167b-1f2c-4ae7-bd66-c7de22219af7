

<?php $__env->startSection('content'); ?>

    <!-- Main content -->
    <section class="content">
        <div class="col-4">
            <div class="card">
                <div class="card-header">
                    <?php if(isset($section)): ?>
                        <h3><?php echo e(__('app.update_section')); ?></h3>
                    <?php else: ?>
                        <h3><?php echo e(__('app.create_section')); ?></h3>

                    <?php endif; ?>

                </div>
                <div class="card-body">
                    
                    <?php if($errors->any()): ?>
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li><?php echo e($error); ?></li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <?php if(isset($section)): ?>
                        <form method="POST" class="form" action="<?php echo e(route('sections.update', $section)); ?>">
                            <?php echo method_field('PUT'); ?>
                    <?php else: ?>
                            <form method="POST" class="form" action="<?php echo e(route('sections.store')); ?>">
                        <?php endif; ?>
                            <?php echo csrf_field(); ?>


                            <div class="form-group">
                                <h5><?php echo e(__('app.Name')); ?> <span class="text-danger">*</span></h5>
                                <div class="controls">
                                    <input type="text" id="name" name="name" class="form-control"
                                        value="<?php if(isset($section) && old('name') == null): ?><?php echo e($section->name); ?><?php else: ?><?php echo e(old('name')); ?><?php endif; ?>"
                                        required autocomplete="name" autofocus>
                                    <div class="help-block"></div>
                                </div>
                                <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="form-control-feedback"><small> <code><?php echo e($message); ?></code> </small>
                                    </div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="flex items-right justify-end mt-4" style="text-align: right">


                                <button type="submit" class="btn btn-primary">
                                    <?php echo e(__('app.save')); ?>

                                </button>
                            </div>

                        </form>
                </div>
            </div>
        </div>
    </section>
    <!-- /.content -->

<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.admin.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH F:\shop\resources\views/admin/sections/create.blade.php ENDPATH**/ ?>