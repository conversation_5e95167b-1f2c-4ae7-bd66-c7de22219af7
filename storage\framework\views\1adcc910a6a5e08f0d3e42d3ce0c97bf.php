

<?php $__env->startSection('content'); ?>


    <!-- Main content -->
    <section class="content">
        <div class="col-12">
            
            <?php if(session('success')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo e(session('success')); ?>

                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <div class="box">
                <div class="box-header with-border">
                    <h3 class="box-title"><?php echo e(__('app.Sections')); ?></h3>

                    <a href="<?php echo e(route('sections.create')); ?>" style="text-align: right"
                        class="waves-effect waves-light btn mb-5 bg-gradient-primary justify-end"><?php echo e(__('app.Add')); ?>

                    </a>

                </div>
                <!-- /.box-header -->
                <div class="box-body">
                    <div class="table-responsive">
                        <table id="example5" class="table table-bordered table-striped" style="width:100%">
                            <thead>
                                <tr>
                                    <th><?php echo e(__('app.Name')); ?></th>
                                    <th class="d-action"><?php echo e(__('app.Action')); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $data; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td><?php echo e($item->name); ?></td>
                                        <td class="d-action">
                                            <a href="<?php echo e(route('sections.edit', $item)); ?>" class="btn btn-warning btn-md">
                                                <span class="glyphicon glyphicon-edit"> <?php echo e(__('app.edit')); ?></span>
                                            </a>
                                            <form method="POST" action="<?php echo e(route('sections.destroy', $item)); ?>"
                                                style="display: inline-block;"
                                                onsubmit="return confirm('هل أنت متأكد من حذف هذا القسم؟')">
                                                <?php echo csrf_field(); ?>
                                                <?php echo method_field('DELETE'); ?>
                                                <button type="submit" class="btn btn-md btn-danger">
                                                    <span class="glyphicon glyphicon-trash"> <?php echo e(__('app.delete')); ?></span>
                                                </button>
                                            </form>
                                        </td>

                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th>Name</th>
                                    <th>Action</th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
                <!-- /.box-body -->
            </div>
            <!-- /.box -->
        </div>

    </section>
    <!-- /.content -->

<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.admin.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH F:\shop\resources\views/admin/sections/index.blade.php ENDPATH**/ ?>