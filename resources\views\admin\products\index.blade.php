@extends('layouts.admin.app')

@section('content')


    <!-- Main content -->
    <section class="content">
        <div class="col-12">



            <div class="box">
                <div class="box-header with-border">
                    <h3 class="box-title">{{ __('app.Products') }}</h3>

                    <a href="{{route('products.create')}}" style="text-align: right"
                        class="waves-effect waves-light btn mb-5 bg-gradient-primary justify-end">{{ __('app.Add') }}
                    </a>

                </div>
                <!-- /.box-header -->
                <div class="box-body">
                    <div class="table-responsive">
                        <table id="example5" class="table table-bordered table-striped" style="width:100%">
                            <thead>
                                <tr>
                                    <th>{{__('app.proname')}}</th>
                                    <th>{{__('app.proimg')}}</th>
                                    <th>{{__('app.proprice')}}</th>
                                    <th>{{__('app.prosection')}}</th>
                                    <th>{{__('app.prodescription')}}</th>
                                    <th>{{__('app.prosize')}}</th>
                                    <th>{{__('app.prounv')}}</th>
                                    <th class="d-action">{{__('app.Action')}}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($data as $item)
                                    <tr>
                                        <td>{{ $item->proname }}</td>
                                        <td>{{ $item->proimg }}</td>
                                        <td>{{ $item->proprice }}</td>
                                        <td>{{ $item->section_id }}</td>
                                        <td>{{ $item->prodescription }}</td>
                                        <td>{{ $item->prosize }}</td>
                                        <td>{{ $item->prounv }}</td>
                                        <td class="d-action">
                                            <a href="{{route('products.edit', $item)}}" class="btn btn-warning btn-md">
                                                <span class="glyphicon glyphicon-edit"> {{__('app.edit')}}</span>
                                            </a>
                                            <form method="POST" action="{{route('products.destroy', $item)}}"
                                                style="display: inline-block;"
                                                onsubmit="return confirm('هل أنت متأكد من حذف هذا القسم؟')">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-md btn-danger">
                                                    <span class="glyphicon glyphicon-trash"> {{__('app.delete')}}</span>
                                                </button>
                                            </form>
                                        </td>

                                    </tr>
                                @endforeach
                            </tbody>

                        </table>
                    </div>
                </div>
                <!-- /.box-body -->
            </div>
            <!-- /.box -->
        </div>

    </section>
    <!-- /.content -->

@endsection